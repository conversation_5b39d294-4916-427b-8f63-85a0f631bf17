import random
from django.shortcuts import render
from rest_framework.views import APIView
from django.views import View
from core.resp import make_response
from maternity_center.models import MaternityCenter
from message.core import get_sms_client
from .exceptions import SMSNetworkException, SMSBusinessException
from .models import SendSMSCodeLog



# 发送短信验证码
class SendSMSCodeView(APIView):

    def post(self, request):
        
        cid = request.data.get('cid')
            
        mc = MaternityCenter.get_maternity_center_by_cid(cid)
        
        if not mc:
            return make_response(code=-1, msg="月子中心不存在")
        
        phone = request.data.get('phone')
        
        if not phone:
            return make_response(code=-1, msg="手机号不能为空")

        code = str(random.randint(100000, 999999))

        try:
            client = get_sms_client()
            result = client.send_verification_code(phone, code)
            SendSMSCodeLog.send_sms_code_log(phone, code, True, result,mc)
            return make_response(code=0, msg="验证码已发送，有效期5分钟")

        except SMSNetworkException as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, e.message,mc)
            return make_response(code=-1, msg="短信服务网络异常")
            
        except SMSBusinessException as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, e.message,mc)
            return make_response(code=-1, msg=e.message)
            
        except Exception as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, str(e),mc)
            return make_response(code=-1, msg="服务器内部错误")
        


# 文件无法访问
class FileErrorView(View):

    def get(self, request):
        return render(request, 'errors/file_404.html', status=404)
    